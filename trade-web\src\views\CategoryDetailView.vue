<template>
  <div class="category-detail">
    <!-- 固定头部 -->
    <HeaderNav :categories="categoryTree" />
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 顶部面包屑和标题区域 -->
        <div class="page-header">
          <!-- 分类标题和统计 -->
          <div class="section-header">
            <h1 class="section-title">
              <span v-if="searchQuery">
                {{ currentLang === 'zh' ? `搜索结果："${searchQuery}"` : `Search Results: "${searchQuery}"` }}
              </span>
              <span v-else-if="currentCategory">
                {{ currentLang === 'zh' ? currentCategory.name : currentCategory.nameEn }}
              </span>
              <span v-else>
                {{ currentLang === 'zh' ? '产品列表' : 'Product List' }}
              </span>
            </h1>
            <div class="product-count">
              {{ currentLang === 'zh' ? '共找到' : 'Found' }} {{ totalProducts }} {{ currentLang === 'zh' ? '个产品' : 'products' }}
            </div>
          </div>
        </div>

        <!-- 左右布局：左侧分类树 + 右侧产品列表 -->
        <div class="content-layout">
          <aside class="left-sidebar">
            <el-tree
              v-if="treeData.length"
              :data="treeData"
              node-key="id"
              :props="treeProps"
              :expand-on-click-node="false"
              :highlight-current="true"
              :default-expanded-keys="defaultExpandedKeys"
              :current-node-key="currentNodeKey"
              @node-click="handleTreeNodeClick"
            />
            <div v-else class="empty-tree">
              {{ currentLang === 'zh' ? '暂无分类' : 'No categories' }}
            </div>
          </aside>

          <section class="products-section">
            <div class="products-list">
              <div v-if="loading" class="loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                {{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}
              </div>

              <div v-else-if="products.length === 0" class="empty-state">
                <el-icon><Box /></el-icon>
                <p>{{ currentLang === 'zh' ? '暂无产品' : 'No products found' }}</p>
              </div>

              <div v-else>
                <ProductCard
                  v-for="item in products"
                  :key="item.product.id"
                  :product="item.product"
                  :company="item.company"
                />

                <div class="pagination-wrapper">
                  <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :total="totalProducts"
                    layout="prev, pager, next, jumper"
                    @current-change="handlePageChange"
                  />
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, Box } from '@element-plus/icons-vue'
import HeaderNav from '../components/HeaderNav.vue'
import ProductCard from '../components/ProductCard.vue'
import { searchProducts } from '../api/product.js'
import { getCategories } from '../api/home.js'
import { useLanguageStore } from '../stores/language.js'

export default {
  name: 'CategoryDetailView',
  components: {
    HeaderNav,
    ProductCard
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const languageStore = useLanguageStore()
    
    const currentPage = ref(1)
    const pageSize = ref(20)
    const loading = ref(false)
    const products = ref([])
    const totalProducts = ref(0)
    const categoryTree = ref([])
    const treeData = computed(() => transformToElTree(categoryTree.value))
    const treeProps = { label: 'name', children: 'children' }
    const defaultExpandedKeys = ref([])
    const currentNodeKey = ref(null)
    const currentCategory = ref(null)
    const searchQuery = ref('')
    const selectedCategoryId = ref(null)
    
    const currentLang = computed(() => languageStore.currentLang)
    
    // 根据分类ID查找分类信息
    const findCategoryById = (categoryId, categories = categoryTree.value) => {
      for (const category of categories) {
        if (category.id === categoryId) return category
        const children = category.sub_categories || category.children || []
        if (children && children.length > 0) {
          const found = findCategoryById(categoryId, children)
          if (found) return found
        }
      }
      return null
    }

    // 查找从根到目标节点的路径
    const findCategoryPathById = (categoryId, categories = categoryTree.value, path = []) => {
      for (const category of categories) {
        const newPath = [...path, category]
        if (category.id === categoryId) return newPath
        const children = category.sub_categories || category.children || []
        if (children && children.length) {
          const found = findCategoryPathById(categoryId, children, newPath)
          if (found) return found
        }
      }
      return null
    }

    // 转换为 el-tree 结构
    const transformToElTree = (categories = []) => {
      return (categories || []).map(c => ({
        id: c.id,
        name: c.name,
        children: transformToElTree(c.sub_categories || c.children || [])
      }))
    }
    
    // 加载分类树数据
    const loadCategories = async () => {
      try {
        const response = await getCategories()
        if (response && response.code === 0) {
          categoryTree.value = response.data || []
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
      }
    }
    
    // 搜索产品
    const searchProductList = async (params) => {
      try {
        loading.value = true
        console.log('搜索产品参数:', params)
        
        const response = await searchProducts(params)
        console.log('产品搜索API响应:', response)
        
        if (response && response.code === 0) {
          const rawData = response.data.data || []
          console.log('原始产品数据:', rawData)
          
          // 转换数据格式，适配ProductCard组件
          products.value = rawData.map(item => {
            const { product, prices, attributes, merchant } = item
            
            // 从属性中获取特定属性值的辅助函数
            const getAttribute = (attrs, name) => {
              const attr = attrs.find(a => a.attribute && a.attribute.name === name)
              return attr ? attr.value : null
            }
            
            // 从商户属性中获取特定属性值
            const getMerchantAttribute = (attrs, name) => {
              const attr = attrs.find(a => a.attribute && a.attribute.name === name)
              return attr ? attr.value : null
            }
            
            // 格式化价格
            const formatPrice = (priceList) => {
              if (!priceList || priceList.length === 0) return '询价'
              const minPrice = Math.min(...priceList.map(p => parseFloat(p.price_min)))
              const maxPrice = Math.max(...priceList.map(p => parseFloat(p.price_max)))
              if (minPrice === maxPrice) {
                return `¥${minPrice}`
              }
              return `¥${minPrice} - ¥${maxPrice}`
            }
            
            // 构建产品对象
            const productData = {
              id: product.id,
              name: getAttribute(attributes, '商品名称') || product.name || '未命名产品',
              nameEn: getAttribute(attributes, '商品名称') || product.name || 'Unnamed Product',
              price: formatPrice(prices),
              prices: prices || [], // 添加原始价格数组
              image: getAttribute(attributes, 'Main Image') || '/src/assets/logo.png',
              description: getAttribute(attributes, '亮点') || '暂无描述',
              descriptionEn: getAttribute(attributes, '亮点') || 'No description',
              minOrder: prices && prices[0] ? prices[0].order_min : 1,
              minOrderUnit: prices && prices[0] ? prices[0].price_unit : '件',
              minOrderUnitEn: prices && prices[0] ? prices[0].price_unit : 'piece',
              stock: '现货', // 默认值，API中没有库存字段
              specifications: null // API中没有规格字段
            }
            
            // 构建商户对象（如果存在）
            let companyData = null
            if (merchant && merchant.attributes) {
              const establishYear = getMerchantAttribute(merchant.attributes, 'Year of Establishment')
              let yearsInBusiness = 0
              if (establishYear) {
                const establishDate = new Date(establishYear)
                if (!isNaN(establishDate.getTime())) {
                  yearsInBusiness = Math.max(0, new Date().getFullYear() - establishDate.getFullYear())
                }
              }
              
              companyData = {
                id: merchant.id,
                name: getMerchantAttribute(merchant.attributes, '企业名称') || merchant.name || '未知商户',
                nameEn: getMerchantAttribute(merchant.attributes, '企业名称') || merchant.name || 'Unknown Merchant',
                avatar: getMerchantAttribute(merchant.attributes, 'Logo') || '/src/assets/logo.png',
                rating: 4.5, // 默认评分，API中没有评分字段
                location: getMerchantAttribute(merchant.attributes, 'Adddress') || '未知地址',
                locationEn: getMerchantAttribute(merchant.attributes, 'Adddress') || 'Unknown Address',
                employeeCount: parseInt(getMerchantAttribute(merchant.attributes, 'Number of Employees')) || 0,
                yearsInBusiness: yearsInBusiness
              }
            }
            
            return {
              product: productData,
              company: companyData
            }
          })
          
          totalProducts.value = response.data.total || 0
          console.log('转换后的产品数据:', products.value)
        } else {
          console.error('搜索产品失败:', response)
          products.value = []
          totalProducts.value = 0
        }
      } catch (error) {
        console.error('搜索产品时发生错误:', error)
        products.value = []
        totalProducts.value = 0
      } finally {
        loading.value = false
      }
    }
    
    // 处理分页
    const handlePageChange = (page) => {
      currentPage.value = page
      // 重新搜索当前页数据
      const params = buildSearchParams()
      searchProductList(params)
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
    
    // 构建搜索参数
    const buildSearchParams = () => {
      const params = {
        page: currentPage.value,
        size: pageSize.value
      }
      
      // 如果有搜索关键词
      if (route.query.search) {
        params.name = route.query.search
        searchQuery.value = route.query.search
      }
      
      // 如果有分类参数（旧逻辑：三级分类路径）
      if (route.query.category) {
        params.category = parseInt(route.query.category)
      }
      if (route.query.sub_category) {
        params.sub_category = parseInt(route.query.sub_category)
      }
      if (route.query.thd_category) {
        params.thd_category = parseInt(route.query.thd_category)
        currentCategory.value = findCategoryById(params.thd_category)
      }

      // 新增：若从热门分类进入，携带 category_id，直接匹配树并映射到对应层级参数
      if (!params.name && route.query.category_id) {
        const id = parseInt(route.query.category_id)
        selectedCategoryId.value = id
        const path = findCategoryPathById(id)
        if (path && path.length) {
          defaultExpandedKeys.value = path.map(n => n.id)
          currentNodeKey.value = id
          // 根据路径长度映射到接口参数
          if (path.length >= 1) params.category = path[0].id
          if (path.length >= 2) params.sub_category = path[1].id
          if (path.length >= 3) params.thd_category = path[2].id
          currentCategory.value = path[path.length - 1]
        }
      }
      
      return params
    }
    
    // 初始化页面
    const initializePage = async () => {
      await loadCategories()
      const params = buildSearchParams()
      await searchProductList(params)
    }
    
    // 点击树节点：只根据点击ID路由，忽略父子关系
    const handleTreeNodeClick = (node) => {
      if (!node || !node.id) return
      selectedCategoryId.value = node.id
      currentPage.value = 1
      router.push({
        path: '/products',
        query: { category_id: node.id }
      })
    }

    // 监听路由变化
    watch(() => route.query, () => {
      currentPage.value = 1
      initializePage()
    }, { deep: true })
    
    // 页面挂载时初始化
    onMounted(() => {
      languageStore.initLanguage()
      initializePage()
    })
    
    // 组件卸载时清理
    onUnmounted(() => {
      // 清理状态，防止内存泄漏
      products.value = []
      totalProducts.value = 0
      categoryTree.value = []
      currentCategory.value = null
      loading.value = false
    })
    
    return {
      products,
      totalProducts,
      currentPage,
      pageSize,
      loading,
      currentLang,
      categoryTree,
      treeData,
      treeProps,
      defaultExpandedKeys,
      currentNodeKey,
      currentCategory,
      searchQuery,
      handlePageChange,
      handleTreeNodeClick
    }
  }
}
</script>

<style lang="scss" scoped>
.category-detail {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
}

.main-content {
  padding-top: 140px; // 调整以适应新的头部高度 (主导航70px + 二级导航50px + 间距20px)
  padding-bottom: 48px;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
  }
  
  .page-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .content-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 24px;
  }

  .left-sidebar {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    height: fit-content;
    position: sticky;
    top: 110px;
  }

  .products-section {
    width: 100%;
  }
}

.breadcrumb {
  margin-bottom: 16px;
  font-size: 14px;
  
  .breadcrumb-item {
    color: #6b7280;
    text-decoration: none;
    cursor: pointer;
    
    &:hover {
      color: #2563eb;
    }
    
    &.active {
      color: #1f2937;
      font-weight: 500;
    }
  }
  
  .separator {
    margin: 0 8px;
    color: #d1d5db;
  }
}

.section-header {
  .section-title {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
  }
  
  .product-count {
    font-size: 16px;
    color: #6b7280;
  }
}

.products-list {
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 48px;
    font-size: 16px;
    color: #6b7280;
  }
  
  .empty-state {
    text-align: center;
    padding: 48px;
    color: #6b7280;
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #d1d5db;
    }
    
    p {
      font-size: 16px;
      margin: 0;
    }
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding: 24px 0;
  }
}

// 响应式适配
@media (max-width: 1366px) and (min-width: 1025px) {
  .main-content {
    padding-top: 125px; // 主导航65px + 二级导航45px + 间距15px
    
    .container {
      max-width: 1200px;
      padding: 0 24px;
    }
  }
}

@media (max-width: 1024px) and (min-width: 969px) {
  .main-content {
    padding-top: 115px; // 主导航60px + 二级导航42px + 间距13px
    
    .container {
      max-width: 1000px;
      padding: 0 20px;
    }
  }
}

@media (max-width: 968px) {
  .main-content {
    padding-top: 180px; // 移动端头部较高
    padding-bottom: 32px;
    
    .container {
      padding: 0 20px;
      max-width: 100%;
    }
    
    .page-header {
      margin-bottom: 24px;
      padding-bottom: 20px;
    }
    
    .content-layout {
      display: block;
    }
  }
  
  .section-header .section-title {
    font-size: 24px;
  }
}
</style> 