<template>
  <div class="company-home">
    <!-- 三层头部 -->
    <!-- 第一层：网站导航 -->
    <div class="global-header">
      <div class="container">
        <div class="header-content">
          <!-- 左侧Logo和站名 -->
          <div class="logo-area" @click="$router.push('/')" style="cursor: pointer;">
            <img src="../assets/logo.png" alt="Logo" class="logo-img" />
            <div class="logo-text">
              <h2>企业出海服务分会</h2>
              <span class="subtitle">华南理工大学广州校友会</span>
            </div>
          </div>
          
          <!-- 右侧按钮区域 -->
          <div class="action-area">
            <div class="action-item">
              <el-icon><User /></el-icon>
              <span>{{ currentLang === 'zh' ? '登录' : 'Sign In' }}</span>
            </div>
            <div class="action-item">
              <el-icon><Message /></el-icon>
              <span>{{ currentLang === 'zh' ? '消息' : 'Messages' }}</span>
            </div>
            <div class="action-item">
              <el-icon><ShoppingCart /></el-icon>
              <span>{{ currentLang === 'zh' ? '购物车' : 'Cart' }}</span>
            </div>
            
            <!-- 语言切换 -->
            <el-dropdown @command="switchLanguage" class="lang-dropdown">
              <span class="action-item">
                {{ (languages.find(l => l.code === currentLang) || languages[0])?.flag }}
                {{ (languages.find(l => l.code === currentLang) || languages[0])?.name }}
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    v-for="lang in languages" 
                    :key="lang.code"
                    :command="lang.code"
                    :class="{ active: currentLang === lang.code }"
                  >
                    {{ lang.flag }} {{ lang.name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 第二层：公司信息 -->
    <div class="company-header">
      <div class="container">
        <div class="company-header-content">
          <!-- 左侧公司信息 -->
          <div class="company-info">
            <img :src="company?.logo || '../assets/logo.png'" alt="Company Logo" class="company-logo" />
            <div class="company-details">
              <h1 class="company-name">{{ currentLang === 'zh' ? company?.name : company?.nameEn }}</h1>
              <div class="company-rating">
                <div class="stars">
                  <span v-for="i in 5" :key="i" class="star" :class="{ filled: i <= Math.floor(company?.rating || 0) }">★</span>
                  <span class="rating-number">{{ company?.rating || 0 }}</span>
                </div>
                <div class="company-badges">
                  <span class="badge verified">{{ currentLang === 'zh' ? '已认证' : 'Verified' }}</span>
                  <span class="badge years">{{ currentLang === 'zh' ? '8年' : '8 Years' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧搜索区域 -->
          <div class="company-search">
            <div class="search-input-wrapper">
              <div class="search-bar">
                <el-input 
                  v-model="searchQuery" 
                  :placeholder="currentLang === 'zh' ? '搜索产品...' : 'Search Products...'"
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon class="search-icon"><Search /></el-icon>
                  </template>
                </el-input>
                <button class="search-button" @click="handleSearch">
                  {{ currentLang === 'zh' ? '搜索' : 'Search' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 第三层：导航菜单 -->
    <div class="company-nav">
      <div class="container">
        <div class="nav-menu">
          <div 
            class="nav-item" 
            :class="{ active: activeTab === 'home' }"
            @click="activeTab = 'home'"
          >
            {{ currentLang === 'zh' ? '首页' : 'Home' }}
          </div>
          <div 
            class="nav-item has-children"
            :class="{ active: activeTab === 'products' }"
            @mouseenter="showProductMenu = true"
            @mouseleave="showProductMenu = false"
            @click="activeTab = 'products'"
          >
            {{ currentLang === 'zh' ? '产品' : 'Products' }}
            <el-icon class="arrow-icon"><ArrowDown /></el-icon>
            
            <!-- 产品分类下拉菜单 -->
            <div v-if="showProductMenu" class="dropdown-menu">
              <div 
                v-for="categoryGroup in companyCategories"
                :key="categoryGroup.parentId"
                class="dropdown-group"
              >
                <!-- 父分类标题 -->
                <div class="dropdown-parent-title">
                  {{ categoryGroup.parentName }}
                </div>
                
                <!-- 子分类列表 -->
                <div 
                  v-for="subCategory in categoryGroup.subCategories"
                  :key="subCategory.id"
                  class="dropdown-sub-item"
                  @click.stop="activeTab = 'products'; selectSubCategory(subCategory)"
                >
                  {{ currentLang === 'zh' ? subCategory.name : subCategory.nameEn }}
                </div>
              </div>
            </div>
          </div>
          <div 
            class="nav-item" 
            :class="{ active: activeTab === 'about' }"
            @click="activeTab = 'about'"
          >
            {{ currentLang === 'zh' ? '关于我们' : 'About Us' }}
          </div>
          <div 
            class="nav-item" 
            :class="{ active: activeTab === 'contact' }"
            @click="activeTab = 'contact'"
          >
            {{ currentLang === 'zh' ? '联系我们' : 'Contact Us' }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="container">
          <div class="loading-content">
            <el-icon class="is-loading" size="32"><Loading /></el-icon>
            <p>{{ currentLang === 'zh' ? '正在加载商户信息...' : 'Loading merchant information...' }}</p>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="container">
          <div class="error-content">
            <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
            <h3>{{ currentLang === 'zh' ? '加载失败' : 'Loading Failed' }}</h3>
            <p>{{ error }}</p>
            <el-button type="primary" @click="loadMerchantData">
              {{ currentLang === 'zh' ? '重新加载' : 'Retry' }}
            </el-button>
          </div>
        </div>
      </div>

      <!-- 正常内容 -->
      <div v-else>
      <!-- 首页内容 -->
      <div v-if="activeTab === 'home'" class="home-content">
        <!-- 轮播图 -->
        <div class="container">
          <div class="banner-section">
            <el-carousel height="400px" indicator-position="outside">
              <el-carousel-item v-for="(banner, index) in company?.banners" :key="index">
                <img :src="banner.image" :alt="banner.title" class="banner-image" />
                <div class="banner-text">
                  <h2>{{ currentLang === 'zh' ? banner.title : banner.titleEn }}</h2>
                  <p>{{ currentLang === 'zh' ? banner.description : banner.descriptionEn }}</p>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
        
        <div class="container">
          <!-- 产品展示区域 -->
          <div class="products-section">
            <div class="section-layout">
              <!-- 左侧分类树 -->
              <div class="category-sidebar">
                <h3 class="sidebar-title">
                  {{ currentLang === 'zh' ? '产品分类' : 'Product Categories' }}
                </h3>
                <div class="tree-wrapper">
                  <div class="all-products" :class="{ active: selectedCategoryId === null }" @click="selectAllProducts">
                    {{ currentLang === 'zh' ? '全部产品' : 'All Products' }}
                  </div>
                  <el-tree
                    :key="treeRenderKey"
                    v-if="categoryElTree && categoryElTree.length"
                    :data="categoryElTree"
                    node-key="id"
                    :props="treeProps"
                    :expand-on-click-node="false"
                    :highlight-current="true"
                    :default-expanded-keys="defaultExpandedKeys"
                    :current-node-key="selectedCategoryId || undefined"
                    @node-click="handleTreeNodeClick"
                  />
                  <div v-else class="empty-tree">
                    {{ currentLang === 'zh' ? '暂无分类' : 'No categories' }}
                  </div>
                </div>
              </div>
              
              <!-- 右侧产品列表 -->
              <div class="products-list">
                <div class="list-header">
                  <h2 class="section-title">
                    <span v-if="isSearchMode">
                      {{ currentLang === 'zh' ? `搜索结果："${searchQuery}"` : `Search Results: "${searchQuery}"` }}
                      <span class="result-count">({{ filteredProducts.length }} {{ currentLang === 'zh' ? '个结果' : 'results' }})</span>
                    </span>
                    <span v-else>
                      {{ selectedCategory ? (currentLang === 'zh' ? selectedCategory.name : selectedCategory.nameEn) : (currentLang === 'zh' ? '全部产品' : 'All Products') }}
                    </span>
                  </h2>
                  <!-- 清除搜索按钮 -->
                  <div v-if="isSearchMode" class="clear-search">
                    <el-button size="small" @click="clearSearch">
                      {{ currentLang === 'zh' ? '清除搜索' : 'Clear Search' }}
                    </el-button>
                  </div>
                </div>
                
                <!-- 产品加载状态 -->
                <div v-if="productsLoading" class="loading-state">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <p>{{ currentLang === 'zh' ? '正在加载产品...' : 'Loading products...' }}</p>
                </div>
                
                <div v-else class="product-grid">
                  <div 
                    v-for="product in paginatedProducts" 
                    :key="product.id" 
                    class="product-card"
                    @click="viewProductDetail(product.id)"
                    style="cursor: pointer;"
                  >
                    <div class="product-image">
                      <img :src="product.image" :alt="currentLang === 'zh' ? product.name : product.nameEn" />
                    </div>
                    <div class="product-info">
                      <h3 class="product-name">{{ currentLang === 'zh' ? product.name : product.nameEn }}</h3>
                      <div class="product-specification">
                        {{ currentLang === 'zh' ? '规格：' : 'Spec:' }} {{ currentLang === 'zh' ? product.specification : product.specificationEn }}
                      </div>
                      <div class="product-price">{{ product.priceRange }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                  <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :page-sizes="[12, 24, 36, 48]"
                    layout="sizes, prev, pager, next, jumper"
                    :total="filteredProducts.length"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品标签页内容 -->
      <div v-if="activeTab === 'products'" class="products-content">
        <div class="container">
          <!-- 产品展示区域 -->
          <div class="products-section">
            <div class="section-layout">
              <!-- 左侧分类树（与首页模块一致） -->
              <div class="category-sidebar">
                <h3 class="sidebar-title">
                  {{ currentLang === 'zh' ? '产品分类' : 'Product Categories' }}
                </h3>
                <div class="tree-wrapper">
                  <div class="all-products" :class="{ active: selectedCategoryId === null }" @click="selectAllProducts">
                    {{ currentLang === 'zh' ? '全部产品' : 'All Products' }}
                    <span class="count">({{ companyProducts.length }})</span>
                  </div>
                  <el-tree
                    ref="productTreeTabRef"
                    :key="treeRenderKey"
                    v-if="categoryElTree && categoryElTree.length"
                    :data="categoryElTree"
                    node-key="id"
                    :props="treeProps"
                    :expand-on-click-node="false"
                    :highlight-current="true"
                    :default-expanded-keys="defaultExpandedKeys"
                    :current-node-key="selectedCategoryId || undefined"
                    @node-click="handleTreeNodeClick"
                  />
                  <div v-else class="empty-tree">
                    {{ currentLang === 'zh' ? '暂无分类' : 'No categories' }}
                  </div>
                </div>
              </div>
              
              <!-- 右侧产品列表 -->
              <div class="products-list">
                <div class="list-header">
                  <h2 class="section-title">
                    {{ selectedCategory ? (currentLang === 'zh' ? selectedCategory.name : selectedCategory.nameEn) : (currentLang === 'zh' ? '全部产品' : 'All Products') }}
                  </h2>
                </div>
                
                <div class="product-grid">
                  <div 
                    v-for="product in paginatedProducts" 
                    :key="product.id" 
                    class="product-card"
                    @click="viewProductDetail(product.id)"
                    style="cursor: pointer;"
                  >
                    <div class="product-image">
                      <img :src="product.image" :alt="currentLang === 'zh' ? product.name : product.nameEn" />
                    </div>
                    <div class="product-info">
                      <h3 class="product-name">{{ currentLang === 'zh' ? product.name : product.nameEn }}</h3>
                      <div class="product-specification">
                        {{ currentLang === 'zh' ? '规格：' : 'Spec:' }} {{ currentLang === 'zh' ? product.specification : product.specificationEn }}
                      </div>
                      <div class="product-price">{{ product.priceRange }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- 分页 -->
                <div class="pagination-wrapper">
                  <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    :page-sizes="[12, 24, 36, 48]"
                    layout="sizes, prev, pager, next, jumper"
                    :total="filteredProducts.length"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关于我们标签页内容 -->
      <div v-if="activeTab === 'about'" class="about-content">
        <div class="container">
          <div class="about-section">
            <div class="company-about-unified-card">
              <div class="about-card-header">
                <h2 class="section-title">{{ currentLang === 'zh' ? '公司简介' : 'Company Profile' }}</h2>
              </div>
              
              <div class="about-card-content">
                <!-- 公司图片与介绍 -->
                <div class="company-info-content">
                  <div class="company-image">
                    <img :src="company?.logo" alt="Company" />
                  </div>
                  <div class="company-description">
                    <p>{{ currentLang === 'zh' ? 
                      company?.description || '我们是一家专业的B2B电子商务企业，致力于为全球买家和供应商提供优质的产品和服务。我们的使命是通过技术创新和优质服务，促进全球贸易的发展，为客户创造更多价值。公司成立于2010年，总部位于中国郑州，是一家集研发、生产、销售为一体的综合性企业。目前，公司拥有员工200余人，工厂面积5000平方米，出口市场120多个。我们的文化是诚信、创新、合作、共赢。' : 
                      company?.descriptionEn || 'We are a professional B2B e-commerce company dedicated to providing quality products and services to global buyers and suppliers. Our mission is to promote the development of global trade through technological innovation and quality services, creating more value for our customers.' }}
                  </p>
                  </div>
                </div>
                
                <!-- 公司统计数据 -->
                <div class="company-stats">
                  <div class="stat-item">
                    <div class="stat-value">{{ company?.establishYear || '--' }}</div>
                    <div class="stat-label">{{ currentLang === 'zh' ? '成立年份' : 'Established' }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ company?.employeeCount || '--' }}</div>
                    <div class="stat-label">{{ currentLang === 'zh' ? '员工数量' : 'Employees' }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ company?.businessType || '--' }}</div>
                    <div class="stat-label">{{ currentLang === 'zh' ? '企业类型' : 'Business Type' }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ formatRegisteredCapital(company) }}</div>
                    <div class="stat-label">{{ currentLang === 'zh' ? '注册资本' : 'Registered Capital' }}</div>
                  </div>
                </div>
                
                <!-- 企业文化 -->
                <div class="company-culture-section">
                  <h3 class="culture-section-title">{{ currentLang === 'zh' ? '企业详情' : 'Company Profile' }}</h3>
                  <div class="culture-content">
                    <!-- 直接渲染企业详情属性 -->
                    <div class="profile-content" v-if="company?.profile">
                      <div v-html="company.profile"></div>
                    </div>
                    <div v-else class="profile-placeholder">
                      <p>{{ currentLang === 'zh' ? '暂无企业详情信息' : 'No company profile information available' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系我们标签页内容 -->
      <div v-if="activeTab === 'contact'" class="contact-content">
        <div class="container">
          <div class="contact-page-layout">
            <!-- 左侧联系信息 -->
            <div class="contact-details-section">
              <h2 class="section-title">{{ currentLang === 'zh' ? '联系详情' : 'Contact Details' }}</h2>
              
              <div class="contact-info-item">
                <div class="info-label">{{ currentLang === 'zh' ? '地址：' : 'Address:' }}</div>
                <div class="info-value">
                  <span>{{ company?.address || (currentLang === 'zh' ?
                    '--' : '--') }}</span>
                  <el-button class="map-btn" size="small" link>
                    <el-icon><LocationFilled /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <!-- <div class="factory-tour">
                <el-button size="small" type="primary" plain>
                  <el-icon><Calendar /></el-icon>
                  {{ currentLang === 'zh' ? '预约参观工厂' : 'Book a Factory Tour' }}
                </el-button>
              </div> -->
              
              <!-- <div class="contact-info-item">
                <div class="info-label">{{ currentLang === 'zh' ? '当地时间：' : 'Local Time:' }}</div>
                <div class="info-value">11:16PM Wed Jun 11</div>
              </div> -->
              
              <div class="contact-info-item">
                <div class="info-label">{{ currentLang === 'zh' ? '电话：' : 'Telephone:' }}</div>
                <div class="info-value">
                  <span v-if="company?.telephone">{{ company.telephone }}</span>
                  <el-button v-else class="sign-btn" size="small" type="danger" plain>
                    {{ currentLang === 'zh' ? '登录查看详情' : 'Sign in for Details' }}
                  </el-button>
                </div>
              </div>

              <div class="contact-info-item">
                <div class="info-label">{{ currentLang === 'zh' ? '手机：' : 'Mobile Phone:' }}</div>
                <div class="info-value">
                  <span v-if="company?.telephone">{{ company.telephone }}</span>
                  <span v-else class="masked">*********</span>
                </div>
              </div>

              <div class="contact-info-item" v-if="company?.website">
                <div class="info-label">{{ currentLang === 'zh' ? '官网：' : 'Website:' }}</div>
                <div class="info-value">
                  <a :href="company.website" target="_blank" class="website-link">{{ company.website }}</a>
                  <el-button class="mobile-site-btn" size="small">
                    <el-icon><Mobile /></el-icon>
                    {{ currentLang === 'zh' ? '移动端' : 'Mobile Site' }}
                  </el-button>
                </div>
              </div>

              <div class="contact-info-item" v-if="!company?.website">
                <div class="info-label">{{ currentLang === 'zh' ? '展厅：' : 'Showroom:' }}</div>
                <div class="info-value">
                  <a href="https://raxida.en.made-in-china.com" target="_blank" class="website-link">https://raxida.en.made-in-china.com</a>
                  <el-button class="mobile-site-btn" size="small">
                    <el-icon><Mobile /></el-icon>
                    {{ currentLang === 'zh' ? '移动端' : 'Mobile Site' }}
                  </el-button>
                </div>
              </div>
            </div>
            
            <!-- 右侧联系供应商 -->
            <div class="contact-supplier-section">
              <h2 class="section-title">{{ currentLang === 'zh' ? '联系供应商' : 'Contact Supplier' }}</h2>
              
              <div class="supplier-info contact-vertical">
                <div class="supplier-name-title">
                  {{ company?.contactName || (currentLang === 'zh' ? '联系人' : 'Contact Person') }}
                </div>
                <div class="supplier-avatar qrcode-box">
                  <img :src="company?.qrCode || 'https://sc04.alicdn.com/kf/H0d49ef6e4456465fbe18e89354b4349fY.jpg'" alt="QR Code" />
                </div>
                <el-button class="contact-now-btn" type="primary" plain>
                  <el-icon><ChatDotRound /></el-icon>
                  {{ currentLang === 'zh' ? '扫码添加微信' : 'Scan to Add WeChat' }}
                </el-button>
              </div>
              
              <!-- <el-button class="contact-now-btn" type="danger">
                <el-icon><Message /></el-icon>
                {{ currentLang === 'zh' ? '立即联系' : 'Contact Now' }}
              </el-button> -->
            </div>
          </div>
        </div>
      </div>
      </div> <!-- 关闭正常内容div -->
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User, Message, ShoppingCart, Search, Loading,
  ArrowDown, ArrowUp, Star, Phone,
  LocationFilled, Calendar, ChatDotRound, Warning
} from '@element-plus/icons-vue'
import { useLanguageStore } from '../stores/language.js'
import { getMerchantDetail, getMerchantCategoryTree, searchMerchantProducts } from '../api/merchant.js'

const route = useRoute()
const router = useRouter()
const languageStore = useLanguageStore()
const currentLang = computed(() => languageStore.currentLang)

// 语言选项
const languages = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
]

// 基础状态
const activeTab = ref('home')
const showProductMenu = ref(false)
const searchQuery = ref('')
const selectedCategoryId = ref(null)
const currentPage = ref(1)
const pageSize = ref(12)
const isSearchMode = ref(false) // 新增：跟踪是否在搜索模式

// API数据状态
const loading = ref(false)
const merchantData = ref(null)
const error = ref(null)
const categoryTreeData = ref([])
const productsData = ref([])
const productsLoading = ref(false)

// 获取公司信息
const company = computed(() => {
  if (!merchantData.value) return null
  
  // 从商户属性中获取特定属性值的辅助函数
  const getMerchantAttribute = (attrs, name) => {
    if (!attrs || !Array.isArray(attrs)) return null
    const attr = attrs.find(a => a.attribute && a.attribute.name === name)
    return attr ? attr.value : null
  }
  
  const merchant = merchantData.value
  const attributes = merchant.attributes || []
  
  return {
    id: merchant.id,
    name: getMerchantAttribute(attributes, '企业名称') || merchant.name || '未知企业',
    nameEn: getMerchantAttribute(attributes, '企业名称') || merchant.name || 'Unknown Company',
    logo: getMerchantAttribute(attributes, 'Logo') || '/src/assets/logo.png',
    mainImage: getMerchantAttribute(attributes, '主页图片') || '/src/assets/logo.png',
    description: getMerchantAttribute(attributes, '简介') || '暂无简介',
    banners: (() => {
      const bannersStr = getMerchantAttribute(attributes, '轮播图')
      if (bannersStr) {
        try {
          const bannerUrls = JSON.parse(bannersStr)
          if (Array.isArray(bannerUrls)) {
            return bannerUrls.map((url, index) => ({
              image: url,
              title: `轮播图 ${index + 1}`,
              titleEn: `Banner ${index + 1}`,
              description: '企业展示',
              descriptionEn: 'Company Showcase'
            }))
          }
        } catch (e) {
          // 如果不是JSON，可能是单个URL
          return [{
            image: bannersStr,
            title: '企业轮播图',
            titleEn: 'Company Banner',
            description: '企业展示',
            descriptionEn: 'Company Showcase'
          }]
        }
      }
      return []
    })(),
    businessType: getMerchantAttribute(attributes, 'BusinessType') || '未知类型',
    mainProducts: getMerchantAttribute(attributes, 'MainProducts') || '暂无',
    employeeCount: getMerchantAttribute(attributes, 'Number of Employees') || '0',
    establishYear: getMerchantAttribute(attributes, 'Year of Establishment') || '未知',
    registeredCapital: getMerchantAttribute(attributes, 'Registered Capital') || '0',
    registeredCapitalCurrency: getMerchantAttribute(attributes, 'Registered Capital Currency') || 'CNY',
    profile: getMerchantAttribute(attributes, 'Profile') || '暂无详情',
    address: getMerchantAttribute(attributes, 'Adddress') || '未知地址',
    telephone: getMerchantAttribute(attributes, 'Telohone') || '未知电话',
    website: getMerchantAttribute(attributes, 'Website') || '',
    homepage: getMerchantAttribute(attributes, 'HomePage') || '',
    contactName: getMerchantAttribute(attributes, 'Contact Name') || '未知联系人',
    qrCode: getMerchantAttribute(attributes, 'QrCode') || '',
    rating: 4.5 // 默认评分，API中没有评分字段
  }
})

// 公司产品分类 - 使用API数据
const companyCategories = computed(() => {
  if (categoryTreeData.value && categoryTreeData.value.length > 0) {
    // 按父分类分组，每个父分类下显示其子分类
    const groupedCategories = []
    categoryTreeData.value.forEach(category => {
      if (category.sub_categories && category.sub_categories.length > 0) {
        // 创建分组对象
        const categoryGroup = {
          parentId: category.id,
          parentName: category.name,
          subCategories: category.sub_categories.filter(sub => sub.level === 1).map(subCategory => ({
            id: subCategory.id,
            name: subCategory.name,
            nameEn: subCategory.name, // 暂时使用中文名
            level: subCategory.level,
            parent_id: subCategory.parent_category_id
          }))
        }
        if (categoryGroup.subCategories.length > 0) {
          groupedCategories.push(categoryGroup)
        }
      }
    })
    console.log('分组后的分类数据:', groupedCategories)
    return groupedCategories
  }
  
  return []
})

// 转换为 el-tree 需要的结构
const treeProps = { label: 'name', children: 'children' }
const categoryElTree = computed(() => {
  const toTree = (groups) => {
    return (groups || []).map(group => ({
      id: group.parentId,
      name: group.parentName,
      children: (group.subCategories || []).map(sub => ({ id: sub.id, name: sub.name }))
    }))
  }
  return toTree(companyCategories.value)
})

// 控制树默认展开哪些节点（当从导航选择二级分类时，需要展开其父级）
const defaultExpandedKeys = ref([])
const treeRenderKey = ref(0)
const productTreeTabRef = ref(null)

// 获取所有二级分类的扁平列表（用于产品计数等）
const flatSubCategories = computed(() => {
  const flat = []
  companyCategories.value.forEach(group => {
    flat.push(...group.subCategories)
  })
  return flat
})

// 获取当前选中的分类
const selectedCategory = computed(() => {
  if (!selectedCategoryId.value) return null
  return companyCategories.value.find(c => c.parentId === selectedCategoryId.value)
})

// 公司产品数据 - 使用API数据
const companyProducts = computed(() => {
  console.log('companyProducts 计算中:')
  console.log('- productsData.value:', productsData.value)

  if (productsData.value && Array.isArray(productsData.value)) {
    const result = productsData.value.map(item => {
      const product = item.product || {} // 获取嵌套的 product 对象
      const attributes = item.attributes || []
      const prices = item.prices || []

      console.log(`- 处理产品 ${product.id}:`)
      console.log(`  - merchant_category_id: ${product.merchant_category_id}`)
      console.log(`  - attributes:`, attributes)

      // 从属性中获取特定属性值的辅助函数（使用attribute_id）
      const getAttributeById = (attrs, attributeId) => {
        const attr = attrs.find(a => a.attribute_id === attributeId)
        return attr ? attr.value : null
      }

      // 格式化价格范围
      const formatPriceRange = (priceList) => {
        if (!priceList || priceList.length === 0) return '询价'

        // 获取所有价格单位，去重并排序
        const priceUnits = priceList.map(p => parseFloat(p.price_unit)).sort((a, b) => a - b)
        const minPrice = Math.min(...priceUnits)
        const maxPrice = Math.max(...priceUnits)
        const currency = priceList[0]?.currency || 'CNY'

        if (minPrice === maxPrice) {
          return `${minPrice} ${currency}`
        }
        return `${minPrice} - ${maxPrice} ${currency}`
      }

      const productData = {
        id: product.id,
        name: getAttributeById(attributes, 1) || product.name, // attribute_id: 1 = 商品名称
        nameEn: getAttributeById(attributes, 1) || product.name,
        specification: getAttributeById(attributes, 7) || '暂无规格', // attribute_id: 7 = Specification
        specificationEn: getAttributeById(attributes, 7) || 'No specification',
        priceRange: formatPriceRange(prices),
        image: getAttributeById(attributes, 4) || '/src/assets/logo.png', // attribute_id: 4 = 主图
        categoryId: product.merchant_category_id, // 使用商户分类ID
        companyId: product.merchant_id
      }

      console.log(`  - 处理后的产品数据:`, productData)
      return productData
    })

    console.log('- 最终产品列表:', result)
    return result
  }

  console.log('- 返回空数组')
  return []
})

// 根据分类筛选产品
const filteredProducts = computed(() => {
  let result = companyProducts.value

  console.log('filteredProducts 计算中:')
  console.log('- companyProducts.value:', companyProducts.value)
  console.log('- selectedCategoryId.value:', selectedCategoryId.value)

  // 应用分类筛选
  if (selectedCategoryId.value) {
    result = result.filter(p => {
      console.log(`- 产品 ${p.id} 的 categoryId: ${p.categoryId}, 选中的分类ID: ${selectedCategoryId.value}`)
      return p.categoryId === selectedCategoryId.value
    })
    console.log('- 筛选后的产品:', result)
  }

  // 默认按最新排序
  result = [...result].sort((a, b) => b.id - a.id)

  console.log('- 最终结果:', result)
  return result
})

// 分页后的产品
const paginatedProducts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredProducts.value.slice(start, end)
})

// 获取分类下的产品数量
const getCategoryProductCount = (categoryId) => {
  return companyProducts.value.filter(p => p.categoryId === categoryId).length
}

// 选择分类
const selectCategory = async (category) => {
  // 兼容旧入口：父分类作为容器不直接筛选
  selectedCategoryId.value = null
  showProductMenu.value = false
  currentPage.value = 1
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    await loadMerchantProducts(merchantId)
  }
}

// 选择全部产品
const selectAllProducts = async () => {
  selectedCategoryId.value = null
  showProductMenu.value = false
  currentPage.value = 1
  isSearchMode.value = false // 清除搜索模式
  searchQuery.value = '' // 清空搜索框
  // 重新加载所有产品数据
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    await loadMerchantProducts(merchantId)
  }
}

// 树节点点击
const handleTreeNodeClick = async (node) => {
  console.log('handleTreeNodeClick 被调用:')
  console.log('- node:', node)

  if (!node || !node.id) return
  // 叶子节点是二级分类，父节点是一级分类
  selectedCategoryId.value = node.children && node.children.length ? null : node.id
  console.log('- 设置 selectedCategoryId.value:', selectedCategoryId.value)

  currentPage.value = 1
  isSearchMode.value = false
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    console.log('- 调用 loadMerchantProducts，参数:', { merchantId, categoryId: selectedCategoryId.value || undefined })
    await loadMerchantProducts(merchantId, selectedCategoryId.value || undefined)
  }
}

// 选择子分类
const selectSubCategory = async (subCategory) => {
  if (!subCategory?.id) return
  selectedCategoryId.value = subCategory.id
  showProductMenu.value = false
  currentPage.value = 1
  isSearchMode.value = false // 清除搜索模式
  searchQuery.value = '' // 清空搜索框
  // 计算并展开父节点
  const parent = companyCategories.value.find(g => g.subCategories?.some(s => s.id === subCategory.id))
  defaultExpandedKeys.value = parent ? [parent.parentId] : []
  treeRenderKey.value++
  // 重新加载该子分类的产品数据
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    await loadMerchantProducts(merchantId, subCategory.id)
  }
  await nextTick()
  try {
    if (productTreeTabRef.value && parent) {
      const parentNode = productTreeTabRef.value.getNode(parent.parentId)
      if (parentNode) productTreeTabRef.value.expandNode(parentNode, true)
      const childNode = productTreeTabRef.value.getNode(subCategory.id)
      if (childNode) productTreeTabRef.value.setCurrentKey(subCategory.id)
    }
  } catch {}
}

// 导航到产品分类
const navToProductCategory = (category) => {
  activeTab.value = 'products'
  selectCategory(category)
}

// 查看产品详情
const viewProductDetail = (productId) => {
  // 阻止事件冒泡，确保只触发一次
  event?.stopPropagation()
  router.push(`/product/${productId}`)
}

// 处理搜索
const handleSearch = async () => {
  const query = searchQuery.value.trim()
  console.log('Search query:', query)
  
  if (!query) {
    // 如果搜索框为空，清除搜索模式
    isSearchMode.value = false
    const merchantId = parseInt(route.params.companyId)
    if (merchantId) {
      await loadMerchantProducts(merchantId)
      // 切换到产品标签页并清除分类选择
      activeTab.value = 'products'
      selectedCategoryId.value = null
    }
    return
  }
  
  // 执行搜索
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    isSearchMode.value = true // 设置为搜索模式
    await searchProducts(merchantId, query)
    // 切换到产品标签页并清除分类选择
    activeTab.value = 'products'
    selectedCategoryId.value = null
  }
}

// 搜索产品
const searchProducts = async (merchantId, searchName) => {
  try {
    productsLoading.value = true
    
    const params = {
      merchant_id: merchantId,
      name: searchName, // 传入搜索关键词
      page: 1,
      size: 100
    }
    
    console.log('正在搜索产品，参数:', params)
    const response = await searchMerchantProducts(params)
    console.log('搜索产品API响应:', response)
    
    if (response && response.code === 0) {
      productsData.value = response.data.data || []
      console.log('搜索结果已设置:', productsData.value)
      
      // 重置分页到第一页
      currentPage.value = 1
    } else {
      console.error('搜索产品失败:', response)
      productsData.value = []
    }
  } catch (err) {
    console.error('搜索产品时发生错误:', err)
    productsData.value = []
  } finally {
    productsLoading.value = false
  }
}

// 切换语言
const switchLanguage = (langCode) => {
  languageStore.setLanguage(langCode)
}

// 格式化注册资本
const formatRegisteredCapital = (company) => {
  if (!company?.registeredCapital) {
    return '5000m²' // 默认工厂面积
  }

  const capital = company.registeredCapital
  const currency = company.registeredCapitalCurrency || 'USD'

  // 格式化数字
  const formatNumber = (num) => {
    const number = parseInt(num)
    if (number >= 1000000) {
      return (number / 1000000).toFixed(1) + 'M'
    } else if (number >= 1000) {
      return (number / 1000).toFixed(0) + 'K'
    }
    return number.toString()
  }

  return `${formatNumber(capital)} ${currency}`
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 监听标签页和分类变化
watch([activeTab, selectedCategoryId], () => {
  // 当切换到产品标签时，更新URL中的分类参数
  if (activeTab.value === 'products' && selectedCategoryId.value) {
    router.push({
      path: route.path,
      query: { 
        ...route.query,
        categoryId: selectedCategoryId.value 
      }
    })
    // 在产品页激活时确保树展开与选中
    nextTick(() => {
      const parent = companyCategories.value.find(g => g.subCategories?.some(s => s.id === selectedCategoryId.value))
      defaultExpandedKeys.value = parent ? [parent.parentId] : []
      treeRenderKey.value++
      nextTick(() => {
        try {
          if (productTreeTabRef.value && parent) {
            const parentNode = productTreeTabRef.value.getNode(parent.parentId)
            if (parentNode) productTreeTabRef.value.expandNode(parentNode, true)
            productTreeTabRef.value.setCurrentKey(selectedCategoryId.value)
          }
        } catch {}
      })
    })
  }
})

// 在点击产品导航菜单项时，切换到产品标签页
const navToProducts = (category) => {
  activeTab.value = 'products'
  if (category) {
    selectedCategoryId.value = category.id
    // 找到父节点并默认展开
    const parent = companyCategories.value.find(g => g.subCategories?.some(s => s.id === category.id))
    defaultExpandedKeys.value = parent ? [parent.parentId] : []
    // 强制重渲后手动展开并选中
    treeRenderKey.value++
    nextTick(() => {
      try {
        if (productTreeTabRef.value && parent) {
          // 展开父节点
          const parentNode = productTreeTabRef.value.getNode(parent.parentId)
          if (parentNode) productTreeTabRef.value.expandNode(parentNode, true)
          // 选中子节点
          const childNode = productTreeTabRef.value.getNode(category.id)
          if (childNode) productTreeTabRef.value.setCurrentKey(category.id)
        }
      } catch {}
    })
  } else {
    defaultExpandedKeys.value = []
  }
}

// 加载商户数据
const loadMerchantData = async () => {
  const merchantId = parseInt(route.params.companyId)
  if (!merchantId) {
    error.value = '无效的商户ID'
    return
  }

  try {
    loading.value = true
    error.value = null

    // 并行加载商户信息和分类树
    const [merchantResponse, categoryResponse] = await Promise.all([
      getMerchantDetail(merchantId),
      getMerchantCategoryTree(merchantId)
    ])

    // 处理商户信息
    if (merchantResponse.code === 0) {
      merchantData.value = merchantResponse.data
    } else {
      error.value = merchantResponse.msg || '获取商户信息失败'
      ElMessage.error(error.value)
    }

    // 处理分类树数据
    if (categoryResponse && categoryResponse.code === 0) {
      categoryTreeData.value = categoryResponse.data
      console.log('商户分类树数据:', categoryResponse.data)
      
      // 加载初始产品数据（所有产品）
      await loadMerchantProducts(merchantId)
    } else {
      console.error('获取分类树失败:', categoryResponse)
    }

  } catch (err) {
    error.value = '网络请求失败，请稍后重试'
    ElMessage.error(error.value)
    console.error('加载商户数据失败:', err)
  } finally {
    loading.value = false
  }
}

// 加载商户产品数据
const loadMerchantProducts = async (merchantId, categoryId = null) => {
  try {
    productsLoading.value = true
    
    const params = {
      merchant_id: merchantId,
      page: 1,
      size: 100 // 获取更多产品用于展示
    }
    
    // 如果指定了分类ID，则只获取该分类的产品
    if (categoryId) {
      params.merchant_category_id = categoryId
    }
    
    console.log('正在加载产品数据，参数:', params)
    const response = await searchMerchantProducts(params)
    console.log('产品数据API响应:', response)
    
    if (response && response.code === 0) {
      productsData.value = response.data.data || []
      console.log('产品数据已设置:', productsData.value)
    } else {
      console.error('获取产品数据失败:', response)
      productsData.value = []
    }
  } catch (err) {
    console.error('加载产品数据失败:', err)
    productsData.value = []
  } finally {
    productsLoading.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  isSearchMode.value = false
  currentPage.value = 1
  // 重新加载所有产品数据
  const merchantId = parseInt(route.params.companyId)
  if (merchantId) {
    loadMerchantProducts(merchantId)
  }
}

// 初始化
onMounted(() => {
  languageStore.initLanguage()

  // 加载商户数据
  loadMerchantData()

  // 如果URL中有分类ID，则选中该分类
  if (route.query.categoryId) {
    selectedCategoryId.value = parseInt(route.query.categoryId)
  }
})

// 监听路由参数变化，重新加载数据
watch(() => route.params.companyId, (newId, oldId) => {
  if (newId !== oldId) {
    loadMerchantData()
  }
})
</script>

<style lang="scss" scoped>
.company-home {
  min-height: 100vh;
  background: #f7f8fa;
}

// 第一层：全局导航
.global-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
  }
  
  .logo-area {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .logo-img {
      width: 36px;
      height: 36px;
    }
    
    .logo-text {
      h2 {
        color: #1e3a8a;
        margin: 0;
        font-size: 16px;
        font-weight: 700;
        line-height: 1.2;
      }
      
      .subtitle {
        font-size: 12px;
        color: #3b82f6;
        display: block;
        margin-top: 2px;
      }
    }
  }
  
  .action-area {
    display: flex;
    align-items: center;
    gap: 24px;
    
    .action-item {
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      padding: 6px 10px;
      border-radius: 6px;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f0f7ff;
        color: #2563eb;
      }
      
      .el-icon {
        font-size: 18px;
      }
      
      span {
        font-size: 14px;
      }
    }
  }
}

// 第二层：公司信息
.company-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .company-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
  }
  
  .company-info {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .company-logo {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      object-fit: cover;
      border: 1px solid #e5e7eb;
    }
    
    .company-details {
      .company-name {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
      }
      
      .company-rating {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stars {
          display: flex;
          align-items: center;
          
          .star {
            color: #d1d5db;
            font-size: 16px;
            
            &.filled {
              color: #fbbf24;
            }
          }
          
          .rating-number {
            margin-left: 8px;
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
          }
        }
        
        .company-badges {
          display: flex;
          gap: 8px;
          
          .badge {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 500;
            
            &.verified {
              background: #dcfce7;
              color: #16a34a;
            }
            
            &.years {
              background: #eff6ff;
              color: #2563eb;
            }
          }
        }
      }
    }
  }
  
  .company-search {
    max-width: 500px;
    width: 100%;
    
    .search-input-wrapper {
      width: 100%;
      
      .search-bar {
        display: flex;
        border: 2px solid #2563eb;
        border-radius: 6px;
        overflow: hidden;
        
        .el-input {
          flex: 1;
          
          :deep(.el-input__wrapper) {
            border: none;
            box-shadow: none !important;
            border-radius: 0;
            
            &.is-focus {
              box-shadow: none !important;
            }
          }
          
          .search-icon {
            color: #6b7280;
          }
        }
        
        .search-button {
          background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
          color: white;
          border: none;
          padding: 0 24px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
          }
        }
      }
    }
  }
}

// 第三层：导航菜单
.company-nav {
  background: #1e40af;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .nav-menu {
    display: flex;
    height: 50px;
    
    .nav-item {
      padding: 0 24px;
      display: flex;
      align-items: center;
      color: white;
      font-weight: 500;
      cursor: pointer;
      position: relative;
      transition: all 0.3s ease;
      
      &:hover, &.active {
        background: rgba(255, 255, 255, 0.1);
      }
      
      &.has-children {
        .arrow-icon {
          margin-left: 6px;
          font-size: 12px;
        }
      }
      
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 200px;
        background: white;
        border-radius: 0 0 6px 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 100;
        
        .dropdown-group {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;
          margin-bottom: 12px;

          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
          }

          .dropdown-parent-title {
            font-size: 14px;
            font-weight: 600;
            color: #3b82f6;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
          }

          .dropdown-sub-item {
            padding: 12px 0;
            font-size: 14px;
            color: #4b5563;
            display: flex;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: #f0f7ff;
              color: #2563eb;
              font-weight: 500;
            }

            .count {
              color: #9ca3af;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 主要内容区域
.main-content {
  margin-top: 20px;
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// 轮播图区域
.banner-section {
  margin-bottom: 40px;
  border-radius: 12px;
  overflow: hidden;

  
  .banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .banner-text {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 24px;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
    color: white;
    
    h2 {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }
}

// 产品展示区域
.products-section {
  .section-layout {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: 30px;
    align-items: flex-start;
  }
  
  // 左侧分类
  .category-sidebar {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 12px;
    height: 100%;
    max-height: 420px;
    position: sticky;
    top: 110px;
    
    .sidebar-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
      padding: 0 4px 8px 4px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .tree-wrapper { padding: 4px; }
    .all-products {
      padding: 10px 12px;
      margin-bottom: 6px;
      font-size: 14px;
      color: #4b5563;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      transition: all .2s ease;
    }
    .all-products:hover, .all-products.active {
      background: #f0f7ff;
      color: #2563eb;
      border-color: #bfdbfe;
      font-weight: 600;
    }
    .empty-tree { color: #9ca3af; font-size: 13px; text-align: center; padding: 12px 0; }
  }
  
  // 右侧产品列表
  .products-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      
      .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
        
        .result-count {
          font-size: 16px;
          font-weight: 400;
          color: #6b7280;
          margin-left: 8px;
        }
      }
      
      .clear-search {
        .el-button {
          background: #f3f4f6;
          border-color: #d1d5db;
          color: #4b5563;
          
          &:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
            color: #374151;
          }
        }
      }
    }
    
    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
      
      .product-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid transparent;
        
        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
          border-color: #2563eb;
        }
        
        .product-image {
          height: 200px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
            
            &:hover {
              transform: scale(1.05);
            }
          }
        }
        
        .product-info {
          padding: 16px;
          
          .product-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.4;
            height: 40px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          
          .product-specification {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 6px;
            line-height: 1.4;
          }

          .product-price {
            font-size: 16px;
            font-weight: 600;
            color: #e11d48;
          }
        }
      }
    }
    
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .products-section .section-layout {
    grid-template-columns: 200px 1fr;
    gap: 20px;
  }
}

@media (max-width: 992px) {
  .company-header .company-header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .company-header .company-search {
    width: 100%;
    max-width: none;
  }
  
  .products-section .section-layout {
    grid-template-columns: 1fr;
  }
  
  .category-sidebar {
    margin-bottom: 20px;
  }
  
  .about-section, .contact-section {
    flex-direction: column;
  }
  
  .company-about-card, .company-culture-card,
  .contact-card, .contact-form-card {
    width: 100%;
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .global-header .header-content {
    flex-direction: column;
    height: auto;
    padding: 16px 0;
    gap: 16px;
  }
  
  .company-info {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
    width: 100%;
    
    .company-logo {
      margin: 0 auto;
    }
    
    .company-details {
      width: 100%;
      text-align: center;
      
      .company-rating {
        justify-content: center;
      }
    }
  }
  
  .company-nav .nav-menu {
    overflow-x: auto;
    justify-content: flex-start;
    
    .nav-item {
      padding: 0 16px;
      white-space: nowrap;
    }
  }
  
  .company-info-content {
    flex-direction: column;
  }
  
  .company-image {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .company-description {
    width: 100%;
  }
}

// 关于我们页面样式
.about-section {
  margin-bottom: 40px;
  
  .company-about-unified-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .about-card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to right, #f8fafc, #ffffff);
    
    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }
  }
  
  .about-card-content {
    padding: 20px;
  }
  
  .company-info-content {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
    
    .company-image {
      width: 25%;
      max-width: 200px;
      
      img {
        width: 100%;
        border-radius: 6px;
        object-fit: cover;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
    
    .company-description {
      width: 75%;
      
      p {
        color: #4b5563;
        line-height: 1.5;
        margin: 0;
        font-size: 15px;
      }
    }
  }
  
  .company-stats {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    
    .stat-item {
      text-align: center;
      padding: 12px 8px;
      background: #f9fafb;
      border-radius: 6px;
      flex: 1;
      border-top: 3px solid #3b82f6;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: translateY(-3px);
      }
      
      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #2563eb;
        margin-bottom: 2px;
      }
      
      .stat-label {
        font-size: 13px;
        color: #6b7280;
      }
    }
  }
  
  .company-culture-section {
    .culture-section-title {
      font-size: 17px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 15px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  
  .culture-content {
    margin-top: 24px;
    
    .profile-content {
      background: white;
      padding: 24px;
      border-radius: 12px;
      border: 1px solid #e5e7eb;
      line-height: 1.8;
      
      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        color: #1f2937;
        margin: 16px 0 12px 0;
        font-weight: 600;
      }
      
      :deep(p) {
        color: #4b5563;
        margin: 12px 0;
        font-size: 14px;
      }
      
      :deep(ul), :deep(ol) {
        margin: 12px 0;
        padding-left: 20px;
        
        li {
          color: #4b5563;
          margin: 6px 0;
          font-size: 14px;
        }
      }
      
      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 12px 0;
      }
    }
    
    .profile-placeholder {
      background: #f9fafb;
      padding: 40px 24px;
      border-radius: 12px;
      text-align: center;
      border: 1px solid #e5e7eb;
      
      p {
        color: #9ca3af;
        margin: 0;
        font-size: 14px;
      }
    }
  }
   
  // 响应式调整
  @media (max-width: 768px) {
    .company-info-content {
      flex-direction: column;
      
      .company-image, .company-description {
        width: 100%;
        max-width: none;
      }
      
      .company-image {
        margin-bottom: 15px;
      }
    }
    
    .company-stats {
      flex-wrap: wrap;
      
      .stat-item {
        flex: 1 0 45%;
        margin-bottom: 10px;
      }
    }
    
    .culture-content .culture-row {
      flex-direction: column;
      
      .culture-item {
        margin-bottom: 10px;
      }
    }
  }
}

// 联系我们页面样式
.contact-section {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 40px;
  
  .contact-card, .contact-form-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 24px;
    flex: 1;
    min-width: 300px;
  }
  
  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 20px 0;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 12px;
  }
  
  .contact-info {
    .contact-item {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      
      .el-icon {
        font-size: 24px;
        color: #2563eb;
        margin-top: 3px;
      }
      
      .contact-text {
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 8px 0;
        }
        
        p {
          color: #4b5563;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
  
  .inquiry-form {
    .el-form-item__label {
      color: #1f2937;
      font-weight: 500;
    }
    
    .el-button {
      width: 100%;
      height: 40px;
      font-weight: 600;
    }
  }
}

/* 联系我们页面样式 */
.contact-page-layout {
  display: flex;
  gap: 30px;
  margin-top: 20px;
}

.contact-details-section {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
}

.contact-supplier-section {
  width: 300px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.contact-info-item {
  display: flex;
  margin-bottom: 15px;
}

.info-label {
  width: 100px;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  display: flex;
  align-items: center;
}

.factory-tour {
  margin: 15px 0 15px 100px;
}

.map-btn {
  margin-left: 10px;
  color: #1890ff;
}

.sign-btn {
  font-size: 13px;
}

.masked {
  color: #999;
}

.website-link {
  color: #1890ff;
  text-decoration: none;
}

.mobile-site-btn {
  margin-left: 10px;
  font-size: 12px;
}

.supplier-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.contact-vertical {
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.supplier-name-title {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
}

.qrcode-box img {
  width: 160px;
  height: 160px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.supplier-avatar {
  width: 70px;
  height: 70px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
}

.supplier-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.supplier-name {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chat-btn {
  font-size: 12px;
}

.contact-now-btn {
  width: 100%;
  height: 40px;
  font-size: 16px;
}

// 加载和错误状态样式
.loading-container, .error-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  .container {
    text-align: center;
  }

  .loading-content, .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    p {
      color: #6b7280;
      margin: 0;
    }

    h3 {
      color: #1f2937;
      margin: 0;
    }
  }
}

// 产品加载状态样式
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 200px;

  .el-icon {
    font-size: 32px;
    color: #409eff;
    margin-bottom: 16px;
  }

  p {
    color: #6b7280;
    margin: 0;
    font-size: 14px;
  }
}
</style> 